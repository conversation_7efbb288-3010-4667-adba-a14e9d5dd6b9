server:
  port: 8085
spring:
  application:
    name: using-common
  cloud:
    nacos:
      discovery:
        # 服务发现
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631  # 服务发现命名空间
      config:
        # 配置中心
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631 # 配置中心命名空间
        file-extension: yaml
        shared-configs:
          - data-id: other-config.yaml
            group: OTHER_GROUP
            refresh: true
# OkHttp配置
okhttp:
  enabled: true
  connect-timeout: 10s
  read-timeout: 30s
  write-timeout: 30s
  call-timeout: 60s
  connection-pool:
    max-idle-connections: 5
    keep-alive-duration: 5m
  retry:
    enabled: true
    max-retries: 3
    retry-interval: 1s
  logging:
    enabled: true
    level: BASIC

# 日志配置
logging:
  level:
    com.example: DEBUG
    lhx.project91.okhttp: DEBUG
    okhttp3: DEBUG

project91:
  global-response:
    enabled: true                    # 启用全局响应处理器
    exception-handler-enabled: false # 禁用全局异常处理器