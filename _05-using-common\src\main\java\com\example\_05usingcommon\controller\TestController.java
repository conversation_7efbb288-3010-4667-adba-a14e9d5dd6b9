package com.example._05usingcommon.controller;

import com.example._05usingcommon.config.NacosConfig;
import com.example._05usingcommon.config.OtherGroupConfig;
import lhx.project91.okhttp.model.HttpResult;
import lhx.project91.okhttp.util.OkHttpTemplate;
import lhx.project91.okhttp.util.OkHttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class TestController {

    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${server.port}")
    private String port;
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello from " + applicationName + " on port " + port;
    }



    @Autowired
    private NacosConfig nacosConfig;

    @Autowired
    private OtherGroupConfig otherGroupConfig;

    @Autowired
    private OkHttpUtil okHttpUtil;

    @Autowired
    private OkHttpTemplate okHttpTemplate;




    /**
     * okhttp测试 - 调用外部API
     */
    @GetMapping("/okhttp")
    public String okhttp() {
        try {
            // 测试调用外部API
            return okHttpUtil.get("https://httpbin.org/get");
        } catch (Exception e) {
            return "OkHttp调用失败: " + e.getMessage();
        }
    }

    /**
     * okhttp测试 - 调用本地其他端口的服务
     */
    @GetMapping("/okhttp-local")
    public String okhttpLocal() {
        try {
            // 如果你有其他服务运行在8080端口，可以调用
            return okHttpUtil.get("http://localhost:8080/hello");
        } catch (Exception e) {
            return "OkHttp本地调用失败: " + e.getMessage();
        }
    }

    /**
     * okhttp测试 - 获取详细结果
     */
    @GetMapping("/okhttp-result")
    public String okhttpResult() {
        try {
            HttpResult<String> result = okHttpUtil.getForResult("https://httpbin.org/get");
            return String.format("状态码: %d, 成功: %s, 耗时: %dms, 数据: %s",
                result.getStatusCode(),
                result.isSuccessful(),
                result.getDuration(),
                result.getData().substring(0, Math.min(100, result.getData().length())) + "..."
            );
        } catch (Exception e) {
            return "OkHttp结果获取失败: " + e.getMessage();
        }
    }

    @GetMapping("/okhttp-template")
    public String okhttpTemplate() {
        try {
            HttpResult<String> result = okHttpTemplate.getForString("https://httpbin.org/get");
            if (result.isSuccessful()) {
                return "OkHttpTemplate成功: 状态码=" + result.getStatusCode() + ", 数据长度=" + result.getData().length();
            } else {
                return "OkHttpTemplate失败: " + result.getErrorMessage();
            }
        } catch (Exception e) {
            return "OkHttpTemplate异常: " + e.getMessage();
        }
    }


    @GetMapping("/get")
    public String getConfig() {

        return "从Nacos获取的配置信息: " + nacosConfig.getConfigInfo();
    }

    @GetMapping("/get-other")
    public String getOtherGroupConfig() {
        return "从Nacos OTHER_GROUP组获取的配置信息: " + otherGroupConfig.getCustomProperty();
    }

    /**
     * 简单的测试接口，用于其他服务调用
     */
    @GetMapping("/test-target")
    public String testTarget() {
        return "这是05项目的测试目标接口，当前时间: " + System.currentTimeMillis();
    }

    /**
     * OkHttp状态检查
     */
    @GetMapping("/okhttp-status")
    public String okhttpStatus() {
        return String.format("OkHttp工具类状态: %s, OkHttpTemplate状态: %s",
            okHttpUtil != null ? "已注入" : "未注入",
            okHttpTemplate != null ? "已注入" : "未注入"
        );
    }
} 
